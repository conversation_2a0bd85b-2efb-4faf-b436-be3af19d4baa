# 项目名
我的智能桌面（MyAIDesktop）

1. 项目技术栈：nodejs + electron + vue2 + element
2. 应用组件 AppComps
    - 应用组件就是把一个个应用封装到组件中，应用内容区加载应用组件来显示
3. 系统组件 SysComps
    - 系统组件用于封装一些能复用的或者逻辑复杂的代码，提高重用性和维护性

# 安装
yarn install （用cnpm会报错）

# 常见问题
- 打包报错：下载winCodeSign失败
  - 解决方法：
  1. 手动下载https://npmmirror.com/mirrors/electron-builder-binaries/winCodeSign-2.6.0/winCodeSign-2.6.0.7z
  2. 解压内容到C:\Users\<USER>\AppData\Local\electron-builder\Cache\winCodeSign\winCodeSign-2.6.0
