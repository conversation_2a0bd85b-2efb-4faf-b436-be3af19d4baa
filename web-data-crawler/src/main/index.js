import { app, shell, BrowserWindow, ipcMain } from 'electron'
var fs = require("fs");
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import process from 'process';

import OSUtil from './util/OSUtil';
import appConfig from './config/app-config';

const ctx = {
  env: 'dev',
  // 是否为本地开发环境
  isLocalDev: false,
  rootPath: __dirname,
  userDataPath: app.getPath('userData'),
  tempPath: app.getPath('temp'),
  logPath: app.getPath('logs'),
  // 对应 package.json 中的 name 和 version
  appName: app.getName(),
  appVersion: app.getVersion(),

  config: appConfig
};

ctx.osUtil = new OSUtil();

ctx.appSetupDirPath = ctx.osUtil.getAppSetupDirPath();


// 识别是否为本地开发环境（用于调试前端项目）
// {
//   const flag1 = (ctx.rootPath.indexOf('out\\main') !== -1) || (ctx.rootPath.indexOf('out/main') !== -1);
//   const flag2 = true
//   ctx.isLocalDev = flag1 && flag2;
//   if (ctx.isLocalDev) console.log(`*** 是本地开发环境 ***`);
// }

if (process.env.NODE_ENV === 'production') {
  console.log('线上环境');
  ctx.env = 'prod';
}
else if (process.env.NODE_ENV === 'test') {
  console.log('测试环境');
  ctx.env = 'test';
}
else {
  console.log('开发环境');
  ctx.env = 'dev';
  ctx.rootPath = ctx.rootPath.replace('/out/main', '');
}

console.log('start app ...');
// 注意：不要在渲染层使用路径，那里访问不到（mac是这样的）
console.log(`rootPath: ${ctx.rootPath}`);
console.log(`appSetupDirPath: ${ctx.appSetupDirPath}`);
console.log(`userDataPath: ${ctx.userDataPath}`);
console.log(`tempPath: ${ctx.tempPath}`);
console.log(`logPath: ${ctx.logPath}`);
console.log(`appName: ${ctx.appName}`);
console.log(`appVersion: ${ctx.appVersion}`);

// *** ipc ***
// 获取应用版本
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})
// 获取配置
ipcMain.handle('get-app-config', () => {
  return ctx.config;
})

// 添加处理外部链接的 IPC 处理程序
ipcMain.handle('open-external-url', async (event, url) => {
  console.log('打开外部链接:', url);
  return await shell.openExternal(url);
})


// 禁用跨域限制
app.commandLine.appendSwitch('disable-site-isolation-trials');

function createWindow() {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1920,
    height: 1440,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      webviewTag: true,
      webSecurity: false, // 禁用安全策略
      nodeIntegration: true,
      contextIsolation: false,
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()

    // Enable opening DevTools in production with keyboard shortcut
    mainWindow.webContents.on('before-input-event', (event, input) => {
      if (input.key.toLowerCase() === 'f12' || (input.control && input.key.toLowerCase() === 'i') || (input.meta && input.key.toLowerCase() === 'i')) {
        mainWindow.webContents.openDevTools();
        event.preventDefault();
      }
    });
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // mainWindow.loadURL('http://localhost:4000/#/cac')
  // mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  // mainWindow.loadFile(join(__dirname, '../../../cs-bot-all-in-one/front/dist/index.html#/cac'))

  if (ctx.isLocalDev === true) {
    const url = 'http://localhost:6001/#/';
    console.log(`打开地址: ${url}`);
    mainWindow.loadURL(url);
  }
  else {
    // HMR for renderer base on electron-vite cli.
    // Load the remote URL for development or the local html file for production.
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
    } else {
      mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window, {
      // Allow DevTools to be opened in production
      devTools: true
    })
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
