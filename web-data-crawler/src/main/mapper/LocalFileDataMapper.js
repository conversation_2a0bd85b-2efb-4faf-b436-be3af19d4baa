import fs from 'fs';
import path from 'path';
/**
 * 本地文件数据映射器
 * 维护单个文件，增删改查
 */
export default class LocalFileDataMapper {
  constructor(ctx, key) {
    this.ctx = ctx;
    this.key = key;
  }

  getFilePath() {
    return `${this.ctx.userDataPath}/data/${this.key}`;
  }

  /**
   * 获取数据
   * @returns {Promise<string | null>} 返回数据字符串，如果不存在则返回null
   */
  async getData() {
    const filePath = this.getFilePath();
    // 检查路径是否存在，存在后才能读取，否则返回null
    if (!fs.existsSync(filePath)) {
      console.log(`文件不存在: ${filePath}`);
      return null;
    }
    try {
      const data = fs.readFileSync(filePath, 'utf8');
      return data;
    } catch (error) {
      console.error(`读取文件异常: ${filePath}`, error);
      return null;
    }
  }

  /**
   * 设置数据
   * @param {string} data - 要保存的数据
   */
  async setData(data) {
    const filePath = this.getFilePath();
    // 解析文件路径的目录路径
    const dirPath = path.dirname(filePath);
    // 检查目录是否存在，不存在则自动创建
    if (!fs.existsSync(dirPath)) {
      console.log(`创建目录: ${dirPath}`);
      await fs.promises.mkdir(dirPath, { recursive: true });
    }
    await fs.promises.writeFile(filePath, data);
  }
}