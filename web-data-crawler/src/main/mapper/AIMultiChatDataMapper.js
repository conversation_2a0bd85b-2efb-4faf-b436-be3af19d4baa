import LocalFileDataMapper from "./LocalFileDataMapper";
/**
 * AI多窗口聊天分组数据映射器
 */
export default class AIMultiChatDataMapper extends LocalFileDataMapper {
  constructor(ctx) {
    super(ctx, 'ai-multi-chat-groups.json');
  }

  /**
   * 获取分组
   * @returns {Array} 分组列表
   */
  async getGroups() {
    const t = await this.getData();
    // console.log('返回内容：', t);
    if (t && t.length > 0) {
      console.log(`从文件读取...`);
      return JSON.parse(t);
    }
    console.log(`从默认值读取...`);
    const def = this.getDefaultGroups();
    this.setGroups(def);
    return def;
  }

  /**
   * 设置分组
   * @param {Array} groups - 分组列表
   */
  async setGroups(groups) {
    await this.setData(JSON.stringify(groups));
  }

  getDefaultGroups() {
    const list = [
        {
          name: "中国AI",
          description: "中国本土AI聊天网站",
          sites: [
            {
              name: 'DeepSeek Chat',
              url: "https://chat.deepseek.com/",
              width: 700
            },
            {
              name: '腾讯元宝',
              url: "https://yuanbao.tencent.com/chat",
              width: 700
            },
          ]
        },
        {
          name: "国外AI",
          description: "国外AI聊天网站",
          sites: [
            {
              name: 'Gemini',
              url: "https://gemini.google.com/app",
              width: 700
            },
            {
              name: 'ChatGPT',
              url: "https://chatgpt.com/",
              width: 700
            },
          ]
        }
      ];
    return list;
  }
}
