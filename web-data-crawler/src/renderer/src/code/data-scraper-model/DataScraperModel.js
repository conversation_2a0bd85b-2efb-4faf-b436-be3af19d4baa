/**
 * 数据抓取器模型
 */
export class DataScraperModel {

  constructor(ctx, data) {
    this.ctx = ctx;
    this.data = data;
    this.iframes = [];
  }

  onStart(pars) {
  }

  start(pars) {
    this.log(`开始抓取...`)
    this.iframes = pars.iframes;
    this.onStart(pars);
  }

  log(msg) {
    if (this.data.onLog) {
      this.data.onLog({
        time: Date.now(),
        content: msg,
      })
    }
  }

  setFrameSrc(index, url) {
    this.iframes[index].src = url;
  }
}
