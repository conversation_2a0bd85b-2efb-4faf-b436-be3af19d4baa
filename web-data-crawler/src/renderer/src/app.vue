<template>
  <home></home>
</template>

<script>
import { defineAsyncComponent, ref } from 'vue';

export default {
  components: {
    'home': defineAsyncComponent(() => import('./pages/home.vue'))
  },
  data() {
    return {
    }
  },
  methods: {
    getCtx() {
      return this.$ctx
    },
  },
  mounted() {
    document.title = `v${this.getCtx().navigatorVersion}`;

    this.$nextTick(function () {
      // 添加F5刷新事件监听
      document.addEventListener('keydown', function (event) {
        if (event.key === 'F5') {
          event.preventDefault(); // 阻止默认的F5刷新行为
          location.reload(); // 手动刷新页面
        }
      });

      document.title = `v${this.getCtx().navigatorVersion}`
    });
  }
}
</script>

<style>
#app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

body {
  padding: 0;
  margin: 0;
  overflow: hidden;
  height: 100vh;
}

html {
  height: 100%;
}
</style>
