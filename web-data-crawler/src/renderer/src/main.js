const { ipc<PERSON><PERSON><PERSON> } = require('electron')
// vue3
import { createApp } from 'vue'
import App from './App.vue'
// ElementPlus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// ...
import appConfig from './config/app-config'

// 获取package中的version
async function getAppVersion() {
  return await ipcRenderer.invoke('get-app-version');
}

// 启动逻辑
async function start() {
  const ctx = {
    navigatorName: 'desktop',
    navigatorVersion: await getAppVersion(),
    imports: {
      ipc<PERSON>enderer
    },
    config: appConfig,
  }

  // *** 获取主进程配置 ***
  ctx.mainProcConfig = await ipcRenderer.invoke('get-app-config');

  // ...
  // ctx.aiMultiChatDataMapper = new AIMultiChatDataMapper(ctx);


  // *** vue3 ***
  const app = createApp(App)
  // 注册ctx到Vue
  app.config.globalProperties.$ctx = ctx
  // 注册ElementPlus
  app.use(ElementPlus)
  // 注册ElementPlus图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }

  app.mount('#app')
}

// 启动
start()

// *** vue2 ***
// import Vue from 'vue'
// import App from './App.vue'
// import router from './router'

// // 加入pinia
// import { createPinia, PiniaVuePlugin } from 'pinia'
// Vue.use(PiniaVuePlugin)
// const pinia = createPinia()

// // 全局加入element-ui
// import ElementUI from 'element-ui';
// import 'element-ui/lib/theme-chalk/index.css';
// Vue.use(ElementUI);

// new Vue({
//   router,
//   pinia,
//   render: h => h(App)
// }).$mount('#app')
