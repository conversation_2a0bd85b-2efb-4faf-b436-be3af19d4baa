<template>
  <div class="iframe-container">
    <div class="iframe-area" style="display: flex;flex-direction: column;flex-grow: 1;">
      <div style="flex-grow: 1;">
        <div class="address-bar">
          <div class="label">窗口0：</div>
          <div class="body">
            <el-input v-model="iframe0.url" placeholder="请输入地址" />
          </div>
        </div>
        <iframe ref="iframe0"
                class="full-iframe"
                frameborder="0"
                scrolling="auto" @load="onIframe0Loaded">
        </iframe>
        <!--              src="https://www.tunnelling.cn/PNews/News.aspx"-->
      </div>
      <div style="height: 1px;background-color: silver;">
      </div>
      <div style="flex-grow: 1;">
        <div class="address-bar">
          <div class="label">窗口1：</div>
          <div class="body">
            <el-input v-model="iframe1.url" placeholder="请输入地址" />
          </div>
        </div>
        <iframe ref="iframe1"
                class="full-iframe"
                frameborder="0"
                scrolling="auto" @load="onIframe1Loaded">
        </iframe>
      </div>
    </div>
    <div style="width: 10px;">&nbsp;</div>
    <div style="width: 1px;background-color: silver;">&nbsp;</div>
    <div style="width: 10px;">&nbsp;</div>
    <div style="display: flex;flex-direction: column;">
      <div class="log-area" style="width: 500px;">
        <div class="header">
          日志
        </div>
        <div class="body">
          <template v-for="log in logs">
            <div>
              <div>{{log.time}}</div>
              <div>{{log.content}}</div>
            </div>
          </template>
        </div>
      </div>
      <div class="ctrl-area">
        <el-button @click="handleStart()">开始抓取</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import { ListFormScraperModel } from '../code/data-scraper-model/ListFormScraperModel'

export default {
  name: 'home',
  components: {},
  data() {
    return {
      iframe0: {
        url: null,
      },
      iframe1: {
        url: null,
      },
      logs: [],
    }
  },
  methods: {
    onIframe0Loaded(e) {
      if (this.$refs.iframe0) {
        console.log('iframe0', this.$refs.iframe0)
        console.log('e', e)
        var contentWindow = e.currentTarget.contentWindow
        console.log(e.currentTarget.contentWindow.document)
        // console.log(e.currentTarget.contentWindow.document.body.innerHTML)

        setTimeout(() => {
          // var selector = $(e.currentTarget.contentWindow.document.body.innerHTML);
          var selector = $(contentWindow.document.body)
          var myPageList = selector.find('.myPageList')

          var boxNewsList = myPageList.find('.box_news')
          // console.log(myPageList.find('.box_news'))

          boxNewsList.each(function(index, item) {
            console.log(item)
          })
        }, 1000)
      }

    },
    onIframe1Loaded(e) {
    },
    pushLog(log) {
      this.logs.splice(0, log);
    },
    handleStart() {
      this.$dataScraper.start({
        iframes: [this.$refs.iframe0, this.$refs.iframe1]
      });
      // this.$refs.iframe0.src = "https://www.tunnelling.cn/PNews/News.aspx"
    }
  },
  mounted() {
    const self = this;
    const ctx = {};
    this.$dataScraper = new ListFormScraperModel(ctx, {
      onLog: (log) => {
        self.pushLog(log);
      }
    });
  }
}
</script>

<style scoped>
.iframe-container {
  width: 100vw;
  height: 100vh;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: row;

  .iframe-area {

    .address-bar {
      display: flex;
      flex-direction: row;
      line-height: 35px;

      .label {
        width: 80px;
        text-align: right;
      }

      .body {
        flex-grow: 1;
      }
    }
  }

  .log-area {
    flex-grow: 1;

    .header {
      line-height: 35px;
      padding-left: 5px;
    }

    .body {
      flex-grow: 1;
      overflow-y: auto;
      height: 0;
    }
  }

  .ctrl-area {
    line-height: 60px;
  }
}

.full-iframe {
  width: 100%;
  height: 100%;
  border: none;
  flex: 1;
  min-height: 0; /* 重要：允许iframe缩小 */
}
</style>
