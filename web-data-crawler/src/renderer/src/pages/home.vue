<template>
  <div class="app-container">
    <!-- 头部标题栏 -->
    <div class="header-bar">
      <div class="title">
        <i class="el-icon-monitor"></i>
        网页数据抓取工具
      </div>
      <div class="actions">
        <el-button type="primary" icon="el-icon-video-play" @click="handleStart()" :loading="isRunning">
          {{ isRunning ? '抓取中...' : '开始抓取' }}
        </el-button>
        <el-button icon="el-icon-refresh" @click="handleClear" circle></el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧iframe区域 -->
      <div class="iframe-section">
        <div class="iframe-panel">
          <div class="panel-header">
            <span class="panel-title">
              <i class="el-icon-view"></i>
              预览窗口 1
            </span>
            <el-button size="mini" icon="el-icon-refresh" @click="refreshIframe(0)" circle></el-button>
          </div>
          <div class="address-bar">
            <el-input
              v-model="iframe0.url"
              placeholder="请输入网页地址..."
              prefix-icon="el-icon-link"
              @keyup.enter="loadIframe(0)"
            >
              <el-button slot="append" icon="el-icon-right" @click="loadIframe(0)"></el-button>
            </el-input>
          </div>
          <div class="iframe-wrapper">
            <iframe
              ref="iframe0"
              class="full-iframe"
              frameborder="0"
              scrolling="auto"
              @load="onIframe0Loaded"
            ></iframe>
            <div v-if="!iframe0.loaded" class="iframe-placeholder">
              <i class="el-icon-loading"></i>
              <p>等待加载网页...</p>
            </div>
          </div>
        </div>

        <div class="divider-horizontal"></div>

        <div class="iframe-panel">
          <div class="panel-header">
            <span class="panel-title">
              <i class="el-icon-view"></i>
              预览窗口 2
            </span>
            <el-button size="mini" icon="el-icon-refresh" @click="refreshIframe(1)" circle></el-button>
          </div>
          <div class="address-bar">
            <el-input
              v-model="iframe1.url"
              placeholder="请输入网页地址..."
              prefix-icon="el-icon-link"
              @keyup.enter="loadIframe(1)"
            >
              <el-button slot="append" icon="el-icon-right" @click="loadIframe(1)"></el-button>
            </el-input>
          </div>
          <div class="iframe-wrapper">
            <iframe
              ref="iframe1"
              class="full-iframe"
              frameborder="0"
              scrolling="auto"
              @load="onIframe1Loaded"
            ></iframe>
            <div v-if="!iframe1.loaded" class="iframe-placeholder">
              <i class="el-icon-loading"></i>
              <p>等待加载网页...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="divider-vertical"></div>

      <!-- 右侧控制面板 -->
      <div class="control-panel">
        <div class="log-section">
          <div class="section-header">
            <span class="section-title">
              <i class="el-icon-document"></i>
              运行日志
            </span>
            <div class="section-actions">
              <el-badge :value="logs.length" class="log-count" type="info"></el-badge>
              <el-button size="mini" icon="el-icon-delete" @click="clearLogs" circle></el-button>
            </div>
          </div>
          <div class="log-content" :update-code="ui.logUpdateCode">
            <div v-if="logs.length === 0" class="empty-logs">
              <i class="el-icon-info"></i>
              <p>暂无日志记录</p>
            </div>
            <div v-else class="log-list">
              <div v-for="(log, index) in logs" :key="index" class="log-item" :class="getLogClass(log.level)">
                <div class="log-time">{{ convertTimeToFormat(log.time) }}</div>
                <div class="log-content-text">{{ log.content }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import { ListFormScraperModel } from '../code/data-scraper-model/ListFormScraperModel'

export default {
  name: 'home',
  components: {},
  data() {
    return {
      iframe0: {
        url: 'https://www.tunnelling.cn/PNews/News.aspx',
        loaded: false,
      },
      iframe1: {
        url: '',
        loaded: false,
      },
      logs: [],
      isRunning: false,
      ui: {
        logUpdateCode: 0,
      }
    }
  },
  methods: {
    onIframe0Loaded(e) {
      this.iframe0.loaded = true;
      this.pushLog({
        time: Date.now(),
        content: '窗口1加载完成',
        level: 'success'
      });

      if (this.$refs.iframe0) {
        console.log('iframe0', this.$refs.iframe0)
        console.log('e', e)
        var contentWindow = e.currentTarget.contentWindow
        console.log(e.currentTarget.contentWindow.document)

        setTimeout(() => {
          var selector = $(contentWindow.document.body)
          var myPageList = selector.find('.myPageList')
          var boxNewsList = myPageList.find('.box_news')

          boxNewsList.each(function(index, item) {
            console.log(item)
          })
        }, 1000)
      }
    },

    onIframe1Loaded(e) {
      this.iframe1.loaded = true;
      this.pushLog({
        time: Date.now(),
        content: '窗口2加载完成',
        level: 'success'
      });
    },

    loadIframe(index) {
      const iframe = index === 0 ? this.iframe0 : this.iframe1;
      const ref = index === 0 ? this.$refs.iframe0 : this.$refs.iframe1;

      if (iframe.url) {
        iframe.loaded = false;
        ref.src = iframe.url;
        this.pushLog({
          time: Date.now(),
          content: `正在加载窗口${index + 1}: ${iframe.url}`,
          level: 'info'
        });
      }
    },

    refreshIframe(index) {
      const ref = index === 0 ? this.$refs.iframe0 : this.$refs.iframe1;
      const iframe = index === 0 ? this.iframe0 : this.iframe1;

      iframe.loaded = false;
      ref.src = ref.src;
      this.pushLog({
        time: Date.now(),
        content: `刷新窗口${index + 1}`,
        level: 'info'
      });
    },

    pushLog(log) {
      this.logs.unshift({
        time: log.time || Date.now(),
        content: log.content,
        level: log.level || 'info'
      });

      // 限制日志数量
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100);
      }

      this.ui.logUpdateCode++;
    },

    clearLogs() {
      this.logs = [];
      this.ui.logUpdateCode++;
    },

    handleClear() {
      this.clearLogs();
      this.$message.success('日志已清空');
    },

    getLogClass(level) {
      return {
        'log-success': level === 'success',
        'log-error': level === 'error',
        'log-warning': level === 'warning',
        'log-info': level === 'info'
      };
    },
    // 把时间戳转为为正常时间格式
    convertTimeToFormat(time) {
      if (!time) {
        return '';
      }

      // 如果传入的是时间戳（数字），转换为Date对象
      let date;
      if (typeof time === 'number') {
        // 判断是秒级时间戳还是毫秒级时间戳
        date = time.toString().length === 10 ? new Date(time * 1000) : new Date(time);
      } else if (typeof time === 'string') {
        // 如果是字符串，尝试解析
        date = new Date(time);
      } else if (time instanceof Date) {
        date = time;
      } else {
        return '';
      }

      // 检查日期是否有效
      if (isNaN(date.getTime())) {
        return '';
      }

      // 格式化为 YYYY-MM-DD HH:mm:ss
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    handleStart() {
      if (this.isRunning) {
        return;
      }

      this.isRunning = true;
      this.pushLog({
        time: Date.now(),
        content: '开始数据抓取任务',
        level: 'info'
      });

      try {
        this.$dataScraper.start({
          iframes: [this.$refs.iframe0, this.$refs.iframe1]
        });
      } catch (error) {
        this.pushLog({
          time: Date.now(),
          content: `抓取失败: ${error.message}`,
          level: 'error'
        });
        this.isRunning = false;
      }
    }
  },
  mounted() {
    const self = this;
    const ctx = {};
    this.$dataScraper = new ListFormScraperModel(ctx, {
      onLog: (log) => {
        self.pushLog(log);
      },
      onComplete: () => {
        self.isRunning = false;
        self.pushLog({
          time: Date.now(),
          content: '数据抓取任务完成',
          level: 'success'
        });
      },
      onError: (error) => {
        self.isRunning = false;
        self.pushLog({
          time: Date.now(),
          content: `抓取出错: ${error.message}`,
          level: 'error'
        });
      }
    });

    // 初始化日志
    this.pushLog({
      time: Date.now(),
      content: '数据抓取工具已就绪',
      level: 'success'
    });

    // 自动加载默认页面
    if (this.iframe0.url) {
      this.$nextTick(() => {
        this.loadIframe(0);
      });
    }
  }
}
</script>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 头部标题栏 */
.header-bar {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.title {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title i {
  font-size: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  padding: 20px;
  gap: 20px;
  min-height: 0;
}

/* iframe区域 */
.iframe-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 0;
}

.iframe-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
  border-radius: 8px 8px 0 0;
}

.panel-title {
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.address-bar {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

.iframe-wrapper {
  flex: 1;
  position: relative;
  min-height: 0;
}

.full-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 0 0 8px 8px;
}

.iframe-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9f9f9;
  color: #909399;
  border-radius: 0 0 8px 8px;
}

.iframe-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  animation: rotate 2s linear infinite;
}

.iframe-placeholder p {
  margin: 0;
  font-size: 14px;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 分隔线 */
.divider-horizontal {
  height: 1px;
  background: linear-gradient(90deg, transparent, #e4e7ed, transparent);
  margin: 0 20px;
}

.divider-vertical {
  width: 1px;
  background: linear-gradient(180deg, transparent, #e4e7ed, transparent);
  margin: 20px 0;
}

/* 控制面板 */
.control-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.log-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
  border-radius: 8px 8px 0 0;
}

.section-title {
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.log-count {
  margin-right: 5px;
}

.log-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.empty-logs {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  padding: 40px;
}

.empty-logs i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-logs p {
  margin: 0;
  font-size: 14px;
}

.log-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.log-item {
  padding: 12px 15px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid #e4e7ed;
  background: #f9f9f9;
  transition: all 0.3s ease;
}

.log-item:hover {
  background: #f0f2f5;
  transform: translateX(2px);
}

.log-item.log-success {
  border-left-color: #67c23a;
  background: #f0f9ff;
}

.log-item.log-error {
  border-left-color: #f56c6c;
  background: #fef0f0;
}

.log-item.log-warning {
  border-left-color: #e6a23c;
  background: #fdf6ec;
}

.log-item.log-info {
  border-left-color: #409eff;
  background: #ecf5ff;
}

.log-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-family: 'Courier New', monospace;
}

.log-content-text {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 10px;
  }

  .control-panel {
    width: 100%;
    height: 300px;
  }

  .iframe-section {
    flex-direction: row;
    height: 400px;
  }

  .divider-horizontal {
    display: none;
  }

  .divider-vertical {
    width: 1px;
    margin: 0 10px;
  }
}
</style>
