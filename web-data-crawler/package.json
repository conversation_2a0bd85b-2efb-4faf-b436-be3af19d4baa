{"name": "cs-ai-client", "version": "1.0.0", "description": "", "main": "./out/main/index.js", "author": "dev", "homepage": "", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "start": "electron-vite preview", "dev_win": "chcp 65001 && electron-vite dev", "dev_other": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack_win": "chcp 65001 && npm run build && electron-builder --dir", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "build": {"artifactName": "setup-${version}.${ext}", "generateUpdatesFilesForAllChannels": false, "win": {"icon": "resources/icon.png", "target": [{"target": "nsis", "arch": ["x64"]}]}, "portable": {}, "nsis": {"oneClick": true, "allowToChangeInstallationDirectory": false, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "forceCodeSigning": false}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "electron-updater": "^6.3.9"}, "devDependencies": {"axios": "^1.6.2", "element-plus": "^2.9.5", "@element-plus/icons-vue": "^2.3.1", "less": "^4.2.0", "less-loader": "^11.1.3", "@electron-toolkit/eslint-config": "^2.0.0", "@electron-toolkit/eslint-config-prettier": "^3.0.0", "@vitejs/plugin-vue": "^5.2.1", "electron": "^29.4.6", "electron-builder": "^25.1.8", "electron-vite": "^3.0.0", "marked": "^15.0.6", "vite": "^6.1.0", "vue": "^3.5.13", "vue-draggable-next": "^2.2.1", "vue-router": "^3.6.5", "jquery": "^3.7.1"}}